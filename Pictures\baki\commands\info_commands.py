import discord
from discord.ext import commands
from discord import app_commands
from database import <PERSON><PERSON><PERSON><PERSON>, User, Character, User<PERSON>haracter
from cache import bot_cache
from datetime import datetime, timedelta
import config
import os

class GalleryView(discord.ui.View):
    def __init__(self, characters, sort_type="rarity"):
        super().__init__(timeout=600)  # Increased timeout to 10 minutes
        self.characters = characters
        self.current_page = 0
        self.sort_type = sort_type
        self.sort_characters()

    def sort_characters(self):
        if self.sort_type == "rarity":
            rarity_order = {'Mythical': 0, 'Legendary': 1, 'Epic': 2, 'Rare': 3, 'Uncommon': 4, 'Common': 5}
            self.characters.sort(key=lambda x: (rarity_order.get(x.rarity, 6), x.name))
        elif self.sort_type == "alpha_asc":
            self.characters.sort(key=lambda x: x.name.lower())
        elif self.sort_type == "alpha_desc":
            self.characters.sort(key=lambda x: x.name.lower(), reverse=True)

    def get_current_character(self):
        if 0 <= self.current_page < len(self.characters):
            return self.characters[self.current_page]
        return None

    def _is_valid_url(self, url):
        """Check if URL is valid and well-formed for Discord embeds"""
        if not url or not isinstance(url, str):
            return False

        # Basic URL validation
        url = url.strip()
        if not url:
            return False

        # Discord only accepts http/https URLs, not file:// or other protocols
        if not (url.startswith('http://') or url.startswith('https://')):
            return False

        # Check for common invalid characters that Discord rejects
        invalid_chars = [' ', '\n', '\r', '\t']
        if any(char in url for char in invalid_chars):
            return False

        return True

    def get_character_image_file(self, character):
        """Get local image file path for character"""
        if not character:
            return None

        # Create safe filename
        safe_name = ''.join(c for c in character.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_').lower()

        # Check for image file
        image_dir = "character_images"
        possible_extensions = ['.jpg', '.jpeg', '.png', '.gif']

        for ext in possible_extensions:
            filepath = os.path.join(image_dir, f"{safe_name}{ext}")
            if os.path.exists(filepath):
                return filepath

        return None

    def create_embed(self, attachment_filename=None):
        try:
            character = self.get_current_character()
            if not character:
                return discord.Embed(title="No characters found", color=0xff0000)

            embed = discord.Embed(
                title=character.name,
                color=config.RARITY_COLORS.get(character.rarity, 0x000000)
            )

            embed.add_field(name="Rarity", value=character.rarity, inline=True)
            embed.add_field(name="Series", value=character.anime_title or "Baki Series", inline=True)
            embed.add_field(name="Page", value=f"{self.current_page + 1}/{len(self.characters)}", inline=True)

            # Set image - prioritize local attachment, then valid URLs
            if attachment_filename:
                embed.set_image(url=f"attachment://{attachment_filename}")
            elif character.image_url and self._is_valid_url(character.image_url):
                embed.set_image(url=character.image_url)

            return embed
        except Exception as e:
            print(f"Error creating gallery embed: {e}")
            return discord.Embed(
                title="Error",
                description="Failed to create character display",
                color=0xff0000
            )

    @discord.ui.button(label="◀", style=discord.ButtonStyle.secondary)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
        else:
            self.current_page = len(self.characters) - 1
        await self._update_gallery_message(interaction)

    @discord.ui.button(label="▶", style=discord.ButtonStyle.secondary)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < len(self.characters) - 1:
            self.current_page += 1
        else:
            self.current_page = 0
        await self._update_gallery_message(interaction)

    async def _update_gallery_message(self, interaction: discord.Interaction):
        """Update gallery message with current character and image"""
        character = self.get_current_character()
        if not character:
            await interaction.response.edit_message(embed=self.create_embed(), view=self)
            return

        # Check for local image file
        local_image = self.get_character_image_file(character)

        if local_image:
            # Send with local image as attachment
            file = discord.File(local_image, filename=f"{character.name.replace(' ', '_')}.jpg")
            embed = self.create_embed(attachment_filename=file.filename)
            await interaction.response.edit_message(embed=embed, view=self, attachments=[file])
        else:
            # No local image, just update embed
            embed = self.create_embed()
            await interaction.response.edit_message(embed=embed, view=self, attachments=[])

    @discord.ui.button(label="Rarity", style=discord.ButtonStyle.primary)
    async def sort_rarity(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.sort_type = "rarity"
        self.current_page = 0
        self.sort_characters()
        await self._update_gallery_message(interaction)

    @discord.ui.button(label="A-Z", style=discord.ButtonStyle.primary)
    async def sort_alpha_asc(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.sort_type = "alpha_asc"
        self.current_page = 0
        self.sort_characters()
        await self._update_gallery_message(interaction)

    @discord.ui.button(label="Z-A", style=discord.ButtonStyle.primary)
    async def sort_alpha_desc(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.sort_type = "alpha_desc"
        self.current_page = 0
        self.sort_characters()
        await self._update_gallery_message(interaction)

class InfoCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def get_character_image_file(self, character):
        """Get local image file for character if it exists"""
        if not character.name:
            return None

        # Clean character name for filename
        safe_name = "".join(c for c in character.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_').lower()

        # Check for image file
        image_dir = "character_images"
        possible_extensions = ['.jpg', '.jpeg', '.png', '.gif']

        for ext in possible_extensions:
            filepath = os.path.join(image_dir, f"{safe_name}{ext}")
            if os.path.exists(filepath):
                return filepath

        return None

    def _is_valid_image_url(self, url):
        """Check if image URL is valid and well-formed for Discord embeds"""
        if not url or not isinstance(url, str):
            return False

        # Basic URL validation
        url = url.strip()
        if not url:
            return False

        # Discord only accepts http/https URLs, not file:// or other protocols
        if not (url.startswith('http://') or url.startswith('https://')):
            return False

        # Check for common invalid characters that Discord rejects
        invalid_chars = [' ', '\n', '\r', '\t']
        if any(char in url for char in invalid_chars):
            return False

        return True

    @app_commands.command(name="gallery", description="Browse all available characters")
    @app_commands.describe(rarity="Filter characters by rarity")
    @app_commands.choices(rarity=[
        app_commands.Choice(name="All Rarities", value="all"),
        app_commands.Choice(name="Common", value="common"),
        app_commands.Choice(name="Uncommon", value="uncommon"),
        app_commands.Choice(name="Rare", value="rare"),
        app_commands.Choice(name="Epic", value="epic"),
        app_commands.Choice(name="Legendary", value="legendary"),
        app_commands.Choice(name="Mythical", value="mythical")
    ])
    async def gallery(self, interaction: discord.Interaction, rarity: app_commands.Choice[str] = None):
        """Display all available characters with button navigation"""
        try:
            # Immediately acknowledge the interaction
            await interaction.response.defer()

            if rarity and rarity.value != "all":
                rarity_name = rarity.value.capitalize()
                characters = bot_cache.get_characters_by_rarity(rarity_name)
                if not characters:
                    await interaction.followup.send(f"No {rarity_name} characters found")
                    return
            else:
                characters = bot_cache.get_all_characters()

            if not characters:
                await interaction.followup.send("No characters found")
                return

            view = GalleryView(characters)

            # Get the first character and check for local image
            first_character = view.get_current_character()
            local_image = view.get_character_image_file(first_character) if first_character else None

            if local_image:
                # Send with local image as attachment
                file = discord.File(local_image, filename=f"{first_character.name.replace(' ', '_')}.jpg")
                embed = view.create_embed(attachment_filename=file.filename)
                await interaction.followup.send(embed=embed, view=view, file=file)
            else:
                # No local image, just send embed
                embed = view.create_embed()
                await interaction.followup.send(embed=embed, view=view)

        except Exception as e:
            print(f"Error in gallery command: {e}")
            import traceback
            traceback.print_exc()

            # Try to send error message
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message("❌ Gallery command failed. Please try again.")
                else:
                    await interaction.followup.send("❌ Gallery command failed. Please try again.")
            except Exception as send_error:
                print(f"Failed to send error message: {send_error}")
    
    @app_commands.command(name="info", description="Get detailed information about a character")
    async def info(self, interaction: discord.Interaction, character_name: str):
        """Display detailed character information"""
        # Immediately acknowledge the interaction
        await interaction.response.defer()

        db = SessionLocal()
        try:
            # Find character using cache
            character = bot_cache.get_character_by_name(character_name)

            if not character:
                # Try partial search
                search_results = bot_cache.search_characters(character_name)
                if search_results:
                    character = search_results[0]  # Take first match
                else:
                    await interaction.followup.send(f"Character '{character_name}' not found!")
                    return
            
            # Check if user owns this character
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            owned_count = 0
            
            if user:
                owned_count = db.query(UserCharacter).filter_by(
                    user_id=user.id,
                    character_id=character.id
                ).count()
            
            # Get total ownership count
            total_owned = db.query(UserCharacter).filter_by(character_id=character.id).count()
            
            embed = discord.Embed(
                title=character.name,
                description=character.anime_title or "Baki Series",
                color=config.RARITY_COLORS.get(character.rarity, 0x000000)
            )
            
            embed.add_field(
                name="Rarity",
                value=character.rarity,
                inline=True
            )
            
            embed.add_field(
                name="Pull Rate",
                value=f"{config.RARITY_RATES.get(character.rarity, 0) * 100:.1f}%",
                inline=True
            )
            
            embed.add_field(
                name="Your Collection",
                value=f"{owned_count} owned",
                inline=True
            )
            
            embed.add_field(
                name="Total Collected",
                value=f"{total_owned} times",
                inline=True
            )
            
            # Sacrifice value
            sacrifice_values = {
                'Common': 50,
                'Uncommon': 100,
                'Rare': 200,
                'Epic': 500,
                'Legendary': 1000
            }
            
            sacrifice_value = sacrifice_values.get(character.rarity, 50)
            embed.add_field(
                name="Sacrifice Value",
                value=f"{sacrifice_value} coins",
                inline=True
            )
            
            embed.add_field(
                name="Character ID",
                value=f"MAL ID: {character.mal_id}",
                inline=True
            )
            
            embed.timestamp = datetime.utcnow()

            # Check for local image file
            local_image = self.get_character_image_file(character)

            if local_image:
                # Send with local image as attachment
                file = discord.File(local_image, filename=f"{character.name.replace(' ', '_')}.jpg")
                embed.set_image(url=f"attachment://{file.filename}")
                await interaction.followup.send(embed=embed, file=file)
            else:
                # Fallback to URL if available and valid
                if character.image_url and self._is_valid_image_url(character.image_url):
                    embed.set_image(url=character.image_url)
                await interaction.followup.send(embed=embed)
            
        finally:
            db.close()
    
    @app_commands.command(name="timers", description="Check your cooldowns and timers")
    async def timers(self, interaction: discord.Interaction):
        """Display user's timers and cooldowns"""
        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            
            if not user:
                await interaction.response.send_message("You haven't started yet! Use `/start` to begin.")
                return
            
            embed = discord.Embed(
                title=f"{interaction.user.display_name}'s Timers",
                color=0x00ff99
            )
            
            # Daily pull timer
            if user.last_daily_pull:
                time_since_last = datetime.utcnow() - user.last_daily_pull
                if time_since_last.total_seconds() < config.DAILY_PULL_COOLDOWN:
                    remaining = config.DAILY_PULL_COOLDOWN - time_since_last.total_seconds()
                    hours = int(remaining // 3600)
                    minutes = int((remaining % 3600) // 60)
                    seconds = int(remaining % 60)
                    
                    embed.add_field(
                        name="Daily Pull Cooldown",
                        value=f"{hours:02d}:{minutes:02d}:{seconds:02d}",
                        inline=False
                    )
                else:
                    embed.add_field(
                        name="Daily Pull",
                        value="✅ Available!",
                        inline=False
                    )
            else:
                embed.add_field(
                    name="Daily Pull",
                    value="✅ Available!",
                    inline=False
                )
            
            # Pull statistics for today
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            pulls_today = user.pulls_today if hasattr(user, 'pulls_today') else 0
            
            embed.add_field(
                name="Pulls Today",
                value=f"{pulls_today}/{config.MAX_PULLS_PER_DAY}",
                inline=True
            )
            
            embed.add_field(
                name="Current Currency",
                value=f"{user.currency} coins",
                inline=True
            )
            
            embed.add_field(
                name="Pull Cost",
                value=f"{config.PULL_COST} coins",
                inline=True
            )
            
            # Account age
            account_age = datetime.utcnow() - user.created_at
            days = account_age.days
            
            embed.add_field(
                name="Account Age",
                value=f"{days} days",
                inline=True
            )
            
            embed.add_field(
                name="Total Pulls",
                value=str(user.total_pulls),
                inline=True
            )
            
            # Collection progress
            total_characters = db.query(Character).count()
            user_unique_characters = db.query(UserCharacter).filter_by(user_id=user.id).distinct(UserCharacter.character_id).count()
            
            completion_rate = (user_unique_characters / total_characters * 100) if total_characters > 0 else 0
            
            embed.add_field(
                name="Collection Progress",
                value=f"{user_unique_characters}/{total_characters} ({completion_rate:.1f}%)",
                inline=True
            )
            
            embed.timestamp = datetime.utcnow()
            
            await interaction.response.send_message(embed=embed)
            
        finally:
            db.close()

    @app_commands.command(name="help", description="Show all available commands")
    async def help(self, interaction: discord.Interaction):
        """Display help information with all commands"""
        embed = discord.Embed(
            title="🥊 Baki Gacha Bot Commands",
            description="Complete command list for the Baki character collection game",
            color=0x00FF00
        )

        embed.add_field(
            name="🎮 Basic Commands",
            value="`/start` - Begin your journey\n"
                  "`/profile` - View your stats and level\n"
                  "`/timers` - Check cooldowns and limits",
            inline=False
        )

        embed.add_field(
            name="🎲 Gacha Commands",
            value="`/pull` - Pull for characters (30/hour limit)\n"
                  "`/inventory` - View your character collection\n"
                  "`/shards` - View character shards inventory\n"
                  "`/upgrade <character>` - Upgrade character tier with shards",
            inline=False
        )

        embed.add_field(
            name="⚔️ Team & Battle Commands",
            value="`/team` - Manage your 5-character team\n"
                  "`/battle [difficulty]` - Challenge AI opponents\n"
                  "Battle daily limit: 10 battles per day",
            inline=False
        )

        embed.add_field(
            name="📚 Info Commands",
            value="`/gallery [rarity]` - Browse all characters\n"
                  "`/info <character>` - Get character details\n"
                  "`/help` - Show this help message",
            inline=False
        )

        embed.add_field(
            name="🌟 New Features",
            value="• **Mythical Rarity** - Ultra rare characters (0.5% rate)\n"
                  "• **Character Shards** - Get shards from duplicates\n"
                  "• **Tier System** - Upgrade characters with shards\n"
                  "• **Player Leveling** - Gain XP from pulls\n"
                  "• **Team Management** - Build your ultimate team\n"
                  "• **Pull Rate Limiting** - 30 pulls per hour",
            inline=False
        )

        embed.add_field(
            name="💎 Rarity Rates",
            value="🌟 Mythical: 0.5%\n🏆 Legendary: 2.5%\n💜 Epic: 7%\n🔵 Rare: 15%\n🟢 Uncommon: 25%\n⚪ Common: 50%",
            inline=True
        )

        embed.add_field(
            name="🔸 Tier Progression",
            value="T0→T1: 1 shard\nT1→T2: 3 shards\nT2→T3: 6 shards\nT3→T4: 10 shards\nAnd so on...",
            inline=True
        )

        embed.set_footer(text="Collect all 35 Baki characters and build the strongest team!")

        await interaction.response.send_message(embed=embed)

async def setup(bot):
    await bot.add_cog(InfoCommands(bot))
