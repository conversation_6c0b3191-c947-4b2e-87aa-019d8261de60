#!/usr/bin/env python3

import sqlite3
import os
from database import SessionLocal, Character

def add_character_power_columns():
    """Add nickname, base_power, and power_growth_rate columns to characters table"""
    print("🔧 Adding character power system columns...")
    
    db_path = 'baki_gacha.db'
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(characters)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add nickname column if it doesn't exist
        if 'nickname' not in columns:
            cursor.execute("ALTER TABLE characters ADD COLUMN nickname TEXT")
            print("✅ Added nickname column")
        else:
            print("✅ nickname column already exists")
        
        # Add base_power column if it doesn't exist
        if 'base_power' not in columns:
            cursor.execute("ALTER TABLE characters ADD COLUMN base_power INTEGER DEFAULT 100")
            print("✅ Added base_power column")
        else:
            print("✅ base_power column already exists")
        
        # Add power_growth_rate column if it doesn't exist
        if 'power_growth_rate' not in columns:
            cursor.execute("ALTER TABLE characters ADD COLUMN power_growth_rate INTEGER DEFAULT 10")
            print("✅ Added power_growth_rate column")
        else:
            print("✅ power_growth_rate column already exists")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Error adding columns: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def set_character_nicknames_and_power():
    """Set nicknames and power stats for all characters based on their identity and rarity"""
    print("🎯 Setting character nicknames and power stats...")
    
    # Character nicknames mapping
    character_nicknames = {
        "Baki Hanma": "The Champion",
        "Yujiro Hanma": "The Ogre", 
        "Jack Hanma": "The Biter",
        "Yuuichirou Hanma": "The Legendary Warrior",
        "Kaoru Hanayama": "The Grip Master",
        "Doppo Orochi": "The Tiger Slayer",
        "Katsumi Orochi": "The Lightning Fast",
        "Retsu Kaiou": "The Chinese Kenpo Master",
        "Kaku Kaiou": "The Supreme Grandmaster",
        "Jaku Kaiou": "The Poison Hand",
        "Goki Shibukawa": "The Master of Aiki",
        "Pickle": "The Primitive Fighter",
        "Miyamoto Musashi": "The Legendary Swordsman",
        "Biscuit Oliva": "Mr. Unchained",
        "Jun Guevara": "The Revolutionary",
        "Nomi no Sukune": "The Sumo Legend",
        "Mohammad Alai Jr.": "The Boxing Prodigy",
        "Spec": "The Apnea Rush",
        "Dorian": "The Hypnotist",
        "Sikorsky": "The Cord Cutter",
        "Hector Doyle": "The Blade",
        "Ryuukou Yanagi": "The Poison Hand",
        "Kosho Shinogi": "The Cord Cutter",
        "Kureha Shinogi": "The Surgeon",
        "Motobe Izou": "The Weapons Master",
        "Tokugawa Mitsunari": "The Underground Arena Owner",
        "Kozue Matsumoto": "The Supporter",
        "Emi Akezawa": "The Reporter",
        "Chiharu Shiba": "The Announcer",
        "Garland": "The Convict",
        "Strydum": "The Soldier",
        "Antonio Igari": "The Pro Wrestler",
        "Mount Toba": "The Sumo Wrestler",
        "Ron Shobun": "The Chinese Fighter"
    }
    
    # Power stats based on rarity (increased differences for top 3 rarities)
    rarity_power_stats = {
        'Mythical': {'base_power': 300, 'growth_rate': 35},
        'Legendary': {'base_power': 225, 'growth_rate': 28},
        'Epic': {'base_power': 175, 'growth_rate': 22},
        'Rare': {'base_power': 125, 'growth_rate': 15},
        'Uncommon': {'base_power': 110, 'growth_rate': 12},
        'Common': {'base_power': 100, 'growth_rate': 10}
    }
    
    db = SessionLocal()
    try:
        characters = db.query(Character).all()
        updated_count = 0
        
        for character in characters:
            # Set nickname
            if character.name in character_nicknames:
                character.nickname = character_nicknames[character.name]
            else:
                # Default nickname based on name
                character.nickname = "Fighter"
            
            # Set power stats based on rarity
            if character.rarity in rarity_power_stats:
                stats = rarity_power_stats[character.rarity]
                character.base_power = stats['base_power']
                character.power_growth_rate = stats['growth_rate']
            else:
                # Default values for unknown rarities
                character.base_power = 100
                character.power_growth_rate = 10
            
            updated_count += 1
            print(f"✅ Updated {character.name}: '{character.nickname}' | {character.rarity} | Power: {character.base_power} (+{character.power_growth_rate}/lvl)")
        
        db.commit()
        print(f"\n🎉 Successfully updated {updated_count} characters with nicknames and power stats!")
        
        # Display summary by rarity
        print("\n📊 Power Stats Summary by Rarity:")
        for rarity, stats in rarity_power_stats.items():
            count = db.query(Character).filter_by(rarity=rarity).count()
            if count > 0:
                print(f"  {rarity}: {count} characters | Base Power: {stats['base_power']} | Growth: +{stats['growth_rate']}/level")
        
    except Exception as e:
        print(f"❌ Error updating characters: {e}")
        db.rollback()
    finally:
        db.close()

def verify_character_updates():
    """Verify that all characters have been updated correctly"""
    print("\n🔍 Verifying character updates...")
    
    db = SessionLocal()
    try:
        characters = db.query(Character).all()
        
        missing_nickname = []
        missing_power = []
        
        for character in characters:
            if not character.nickname:
                missing_nickname.append(character.name)
            if not character.base_power or character.base_power == 0:
                missing_power.append(character.name)
        
        if missing_nickname:
            print(f"⚠️ Characters missing nicknames: {missing_nickname}")
        else:
            print("✅ All characters have nicknames")
        
        if missing_power:
            print(f"⚠️ Characters missing power stats: {missing_power}")
        else:
            print("✅ All characters have power stats")
        
        # Show some examples
        print("\n📋 Sample Characters:")
        sample_chars = db.query(Character).limit(5).all()
        for char in sample_chars:
            print(f"  {char.name} '{char.nickname}' | {char.rarity} | Power: {char.base_power} (+{char.power_growth_rate}/lvl)")
        
    except Exception as e:
        print(f"❌ Error verifying updates: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Character Power System Setup")
    print("=" * 50)
    
    # Step 1: Add database columns
    if add_character_power_columns():
        print("\n" + "=" * 50)
        
        # Step 2: Set nicknames and power stats
        set_character_nicknames_and_power()
        
        # Step 3: Verify updates
        verify_character_updates()
        
        print("\n🎯 Character power system setup complete!")
    else:
        print("❌ Failed to add database columns. Aborting.")
