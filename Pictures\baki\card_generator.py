#!/usr/bin/env python3
"""
Trading Card Generator with Rarity Frames
Creates trading card sized images (2.5x3.5 inches) with rarity-based frames
"""

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import math

# Trading card dimensions (2.5" x 3.5" at 300 DPI)
CARD_WIDTH = 750  # 2.5 * 300
CARD_HEIGHT = 1050  # 3.5 * 300

# Image area (leaving space for frame)
IMAGE_MARGIN = 40
IMAGE_WIDTH = CARD_WIDTH - (IMAGE_MARGIN * 2)
IMAGE_HEIGHT = CARD_HEIGHT - (IMAGE_MARGIN * 2)

# Rarity color schemes (two-tone gradients) - Expanded 8-tier system
RARITY_COLORS = {
    'Common': {
        'top': (160, 160, 160),      # Light gray
        'bottom': (100, 100, 100),   # Dark gray
        'accent': (200, 200, 200)
    },
    'Uncommon': {
        'top': (144, 238, 144),      # Light green
        'bottom': (34, 139, 34),     # Dark green
        'accent': (50, 205, 50)
    },
    'Rare': {
        'top': (135, 206, 250),      # Light blue
        'bottom': (25, 25, 112),     # Dark blue
        'accent': (70, 130, 180)
    },
    'Epic': {
        'top': (221, 160, 221),      # Light purple
        'bottom': (75, 0, 130),      # Dark purple
        'accent': (138, 43, 226)
    },
    'Legendary': {
        'top': (255, 215, 0),        # Light gold
        'bottom': (184, 134, 11),    # Dark gold
        'accent': (255, 165, 0)
    },
    'Mythical': {
        'top': (255, 182, 193),      # Light pink/red
        'bottom': (139, 0, 0),       # Dark red
        'accent': (220, 20, 60)
    },
    'Transcendent': {
        'top': (224, 255, 255),      # Light cyan
        'bottom': (0, 139, 139),     # Dark cyan
        'accent': (0, 255, 255)
    },
    'Divine': {
        'top': (255, 255, 255),      # Pure white
        'bottom': (192, 192, 192),   # Silver
        'accent': (255, 215, 0)      # Gold accent
    }
}

def create_gradient_background(width, height, top_color, bottom_color):
    """Create a vertical gradient background"""
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    for y in range(height):
        # Calculate blend ratio (0.0 at top, 1.0 at bottom)
        ratio = y / height
        
        # Interpolate between top and bottom colors
        r = int(top_color[0] * (1 - ratio) + bottom_color[0] * ratio)
        g = int(top_color[1] * (1 - ratio) + bottom_color[1] * ratio)
        b = int(top_color[2] * (1 - ratio) + bottom_color[2] * ratio)
        
        draw.line([(0, y), (width, y)], fill=(r, g, b))
    
    return image

def enhance_character_image(image_path):
    """Apply sharpness and vibrancy effects to character image"""
    try:
        image = Image.open(image_path)
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Enhance sharpness
        sharpness_enhancer = ImageEnhance.Sharpness(image)
        image = sharpness_enhancer.enhance(1.3)  # 30% more sharp
        
        # Enhance color saturation (vibrancy)
        color_enhancer = ImageEnhance.Color(image)
        image = color_enhancer.enhance(1.2)  # 20% more vibrant
        
        # Enhance contrast slightly
        contrast_enhancer = ImageEnhance.Contrast(image)
        image = contrast_enhancer.enhance(1.1)  # 10% more contrast
        
        return image
    except Exception as e:
        print(f"Error enhancing image {image_path}: {e}")
        return None

def create_frame_with_shadow(width, height, rarity):
    """Create a frame with drop shadow effect"""
    # Create shadow layer
    shadow_offset = 8
    shadow_blur = 6
    shadow_width = width + shadow_offset * 2
    shadow_height = height + shadow_offset * 2
    
    # Create shadow
    shadow = Image.new('RGBA', (shadow_width, shadow_height), (0, 0, 0, 0))
    shadow_draw = ImageDraw.Draw(shadow)
    
    # Draw shadow rectangle
    shadow_draw.rectangle([
        shadow_offset, shadow_offset,
        shadow_width - shadow_offset, shadow_height - shadow_offset
    ], fill=(0, 0, 0, 100))  # Semi-transparent black
    
    # Blur the shadow
    shadow = shadow.filter(ImageFilter.GaussianBlur(radius=shadow_blur))
    
    # Create main frame with gradient
    colors = RARITY_COLORS.get(rarity, RARITY_COLORS['Common'])
    frame = create_gradient_background(width, height, colors['top'], colors['bottom'])
    
    # Add frame border
    frame_draw = ImageDraw.Draw(frame)
    border_width = 6
    
    # Outer border (accent color)
    frame_draw.rectangle([
        0, 0, width - 1, height - 1
    ], outline=colors['accent'], width=border_width)
    
    # Inner border (lighter)
    inner_margin = border_width + 2
    frame_draw.rectangle([
        inner_margin, inner_margin,
        width - inner_margin - 1, height - inner_margin - 1
    ], outline=colors['top'], width=2)
    
    # Combine shadow and frame
    result = Image.new('RGBA', (shadow_width, shadow_height), (0, 0, 0, 0))
    result.paste(shadow, (0, 0))
    result.paste(frame, (shadow_offset, shadow_offset))
    
    return result

def create_trading_card(character_image_path, character_name, rarity, output_path):
    """Create a trading card with enhanced character image and rarity frame"""
    try:
        # Enhance the character image
        enhanced_image = enhance_character_image(character_image_path)
        if not enhanced_image:
            print(f"Failed to enhance image for {character_name}")
            return False
        
        # Resize character image to fit card
        enhanced_image = enhanced_image.resize((IMAGE_WIDTH, IMAGE_HEIGHT), Image.Resampling.LANCZOS)
        
        # Create the frame with shadow
        frame = create_frame_with_shadow(CARD_WIDTH, CARD_HEIGHT, rarity)
        
        # Create final card
        card = Image.new('RGBA', frame.size, (255, 255, 255, 0))
        
        # Paste the frame
        card.paste(frame, (0, 0))
        
        # Calculate position to center the character image
        shadow_offset = 8
        image_x = shadow_offset + IMAGE_MARGIN
        image_y = shadow_offset + IMAGE_MARGIN
        
        # Paste the enhanced character image
        card.paste(enhanced_image, (image_x, image_y))
        
        # Convert to RGB for saving as JPEG
        final_card = Image.new('RGB', card.size, (255, 255, 255))
        final_card.paste(card, mask=card.split()[-1] if card.mode == 'RGBA' else None)
        
        # Save the trading card
        final_card.save(output_path, 'JPEG', quality=95)
        print(f"Created trading card for {character_name} ({rarity}) -> {output_path}")
        return True
        
    except Exception as e:
        print(f"Error creating trading card for {character_name}: {e}")
        return False

def process_all_character_images():
    """Process all character images into trading cards"""
    input_dir = "character_images"
    output_dir = "character_cards"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get character data from database
    import sqlite3
    conn = sqlite3.connect('baki_gacha.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT name, rarity FROM characters')
    characters = cursor.fetchall()
    conn.close()
    
    # Create character name to rarity mapping
    char_rarity_map = {name.lower().replace(' ', '_'): rarity for name, rarity in characters}
    
    processed_count = 0
    total_count = 0
    
    # Process each image file
    if os.path.exists(input_dir):
        for filename in os.listdir(input_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                total_count += 1
                
                # Extract character name from filename
                char_key = filename.lower().replace('.jpg', '').replace('.jpeg', '').replace('.png', '')
                
                # Find matching character and rarity
                rarity = char_rarity_map.get(char_key, 'Common')
                
                input_path = os.path.join(input_dir, filename)
                output_filename = f"{char_key}_card.jpg"
                output_path = os.path.join(output_dir, output_filename)
                
                # Create trading card
                if create_trading_card(input_path, char_key, rarity, output_path):
                    processed_count += 1
    
    print(f"\nTrading card generation complete!")
    print(f"Processed: {processed_count}/{total_count} images")
    print(f"Cards saved to: {output_dir}/")

if __name__ == "__main__":
    process_all_character_images()
