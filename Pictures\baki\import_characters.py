#!/usr/bin/env python3
"""
Import real Baki characters from JSON
"""

import json
from database import SessionLocal, Character, init_database

def import_characters():
    """Import characters from JSON file"""
    init_database()
    
    # Load characters from JSON
    with open('real_baki_characters.json', 'r', encoding='utf-8') as f:
        characters_data = json.load(f)
    
    db = SessionLocal()
    try:
        print(f"Importing {len(characters_data)} characters...")
        
        for i, char_data in enumerate(characters_data, 1):
            character = Character(
                mal_id=i,  # Use sequential IDs since these are custom characters
                name=char_data['name'],
                anime_title=char_data['anime_title'],
                rarity=char_data['rarity'],
                image_url=char_data['image_url']
            )
            db.add(character)
        
        db.commit()
        
        # Verify import
        total_chars = db.query(Character).count()
        print(f"✅ Successfully imported {total_chars} characters!")
        
        # Show rarity distribution
        from sqlalchemy import func
        rarity_counts = db.query(
            Character.rarity, 
            func.count(Character.id)
        ).group_by(Character.rarity).all()
        
        print("\nRarity distribution:")
        for rarity, count in rarity_counts:
            print(f"  {rarity}: {count}")
        
    except Exception as e:
        print(f"❌ Error importing characters: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    import_characters()
