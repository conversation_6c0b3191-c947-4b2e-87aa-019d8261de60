# Baki Gacha Discord Bot

A Discord bot featuring a gacha system for collecting Baki anime characters. The bot uses the Jikan API to fetch real character data and implements a complete gacha game experience.

## Features

- **Character Collection**: Pull for Baki characters with different rarities
- **Rarity System**: Common, Uncommon, Rare, Epic, and Legendary characters
- **User Profiles**: Track your collection, currency, and statistics with user types (<PERSON>, <PERSON>er, <PERSON>, Owner)
- **Inventory Management**: View and manage your collected characters
- **Character Information**: Detailed info about each character
- **Sacrifice System**: Trade characters for currency
- **Timer System**: Daily pull cooldowns and progress tracking
- **Interactive Gallery**: Browse all characters with button-based navigation and sorting (rarity, A-Z, Z-A)
- **Minimalist Design**: Clean, focused interface with no unnecessary elements

## Commands

### Basic Commands
- `/start` - Start your gacha journey
- `/profile` - View your profile and stats
- `/commands` - Show all available commands

### Gacha Commands
- `/pull` - Pull for a random character
- `/inventory [page]` - View your character collection
- `/sacrifice <character_name>` - Sacrifice a character for currency

### Information Commands
- `/gallery [rarity]` - Browse all available characters with interactive navigation
- `/info <character_name>` - Get detailed character information
- `/timers` - Check your cooldowns and timers

## Setup Instructions

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   - The `.env` file is already configured with your bot token
   - Make sure your Discord bot has the necessary permissions

3. **Initialize Database**:
   ```bash
   python jikan_api.py
   ```
   This will populate the character database with Baki characters from the Jikan API.

4. **Run the Bot**:
   ```bash
   python bot.py
   ```

## Bot Permissions Required

- Send Messages
- Use Slash Commands
- Embed Links
- Attach Files
- Read Message History

## Database

The bot uses SQLite with SQLAlchemy ORM for data persistence:
- **Users**: Discord user data, currency, statistics, user type, start date
- **Characters**: Character information from Jikan API
- **UserCharacters**: User's character collection
- **PullHistory**: Record of all character pulls

## Rarity System

- **Common**: 60% chance, 50 coins sacrifice value
- **Uncommon**: 25% chance, 100 coins sacrifice value
- **Rare**: 10% chance, 200 coins sacrifice value
- **Epic**: 4% chance, 500 coins sacrifice value
- **Legendary**: 1% chance, 1000 coins sacrifice value

## Configuration

Key settings in `config.py`:
- Pull cost: 100 coins
- Starting currency: 1000 coins
- Daily pull cooldown: 24 hours
- Maximum pulls per day: 10

## File Structure

```
baki/
├── bot.py              # Main bot file
├── config.py           # Configuration settings
├── database.py         # Database models and setup
├── jikan_api.py        # Jikan API integration
├── requirements.txt    # Python dependencies
├── .env               # Environment variables
└── commands/
    ├── __init__.py
    ├── user_commands.py    # Profile, start, commands
    ├── gacha_commands.py   # Pull, inventory, sacrifice
    └── info_commands.py    # Gallery, info, timers
```
