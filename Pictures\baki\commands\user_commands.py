import discord
from discord.ext import commands
from discord import app_commands
from database import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Character
from bot import get_or_create_user
from datetime import datetime, timedelta
import config

class UserCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
    
    @app_commands.command(name="start", description="Start your Baki gacha journey")
    async def start(self, interaction: discord.Interaction):
        """Initialize a new user account"""
        # Determine user type based on Discord roles or ID
        user_type = self.determine_user_type(interaction.user)
        user = get_or_create_user(str(interaction.user.id), interaction.user.display_name, user_type)

        embed = discord.Embed(
            title="Welcome to Baki Gacha",
            description=f"{interaction.user.display_name} • {user_type}",
            color=0x00ff00
        )

        embed.add_field(
            name="Currency",
            value=f"{user.currency} coins",
            inline=True
        )

        await interaction.response.send_message(embed=embed)

    def determine_user_type(self, user):
        """Determine user type based on Discord user"""
        # Owner check (replace with actual owner ID)
        if user.id == *********:  # Replace with actual owner Discord ID
            return "Owner"

        # Check for specific roles if in a guild
        if hasattr(user, 'roles'):
            role_names = [role.name.lower() for role in user.roles]
            if any(role in role_names for role in ['dev', 'developer', 'admin']):
                return "Dev"
            elif any(role in role_names for role in ['supporter', 'premium', 'vip']):
                return "Supporter"

        return "Player"
    
    @app_commands.command(name="profile", description="View your profile and stats")
    async def profile(self, interaction: discord.Interaction):
        """Display user profile information"""
        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            
            if not user:
                await interaction.response.send_message("You haven't started yet! Use `/start` to begin.")
                return
            
            # Get character count by rarity
            character_counts = {}
            for rarity in config.RARITY_RATES.keys():
                count = db.query(UserCharacter).join(Character).filter(
                    UserCharacter.user_id == user.id,
                    Character.rarity == rarity
                ).count()
                character_counts[rarity] = count
            
            total_characters = sum(character_counts.values())
            
            embed = discord.Embed(
                title=f"{interaction.user.display_name}",
                description=f"{user.user_type} • Started {user.start_date.strftime('%Y-%m-%d')}",
                color=0x0099ff
            )

            embed.add_field(
                name="Currency",
                value=f"{user.currency} coins",
                inline=True
            )

            embed.add_field(
                name="Player Level",
                value=f"Level {user.level} ({user.xp:,} XP)",
                inline=True
            )

            embed.add_field(
                name="Pulls",
                value=f"{user.pulls_remaining}/{config.PULLS_PER_HOUR} remaining\nTotal: {user.total_pulls}",
                inline=True
            )

            embed.add_field(
                name="Characters",
                value=str(total_characters),
                inline=True
            )
            
            # Collection breakdown
            if any(character_counts.values()):
                collection_text = ", ".join([f"{rarity}: {count}" for rarity, count in character_counts.items() if count > 0])
                embed.add_field(
                    name="Collection",
                    value=collection_text,
                    inline=False
                )

            # Add human-readable join date
            join_date = user.created_at.strftime("%B %d, %Y")
            days_since_join = (datetime.utcnow() - user.created_at).days
            embed.add_field(
                name="Member Since",
                value=f"{join_date}\n({days_since_join} days ago)",
                inline=True
            )

            embed.set_thumbnail(url=interaction.user.display_avatar.url)
            
            await interaction.response.send_message(embed=embed)
            
        finally:
            db.close()
    
    @app_commands.command(name="commands", description="List all available commands")
    async def commands_list(self, interaction: discord.Interaction):
        """Display all available bot commands"""
        commands_text = "`/start`, `/profile`, `/help`, `/pull`, `/inventory`, `/shards`, `/upgrade`, `/team`, `/gallery`, `/info`, `/timers`"

        await interaction.response.send_message(commands_text)

async def setup(bot):
    await bot.add_cog(UserCommands(bot))
