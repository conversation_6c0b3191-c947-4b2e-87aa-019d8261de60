import requests
import time
import random
import os
from urllib.parse import urlparse
from database import SessionLocal, Character
from config import JIKAN_BASE_URL, RARITY_RATES

class JikanAPI:
    def __init__(self):
        self.base_url = JIKAN_BASE_URL
        self.session = requests.Session()
        self.images_dir = "character_images"

        # Create images directory if it doesn't exist
        if not os.path.exists(self.images_dir):
            os.makedirs(self.images_dir)
            print(f"Created {self.images_dir} directory")
        
    def _make_request(self, endpoint, params=None):
        """Make a request to Jikan API with rate limiting"""
        url = f"{self.base_url}/{endpoint}"
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            # Respect rate limits
            time.sleep(1)
            
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None
    
    def search_baki_characters(self):
        """Search for Baki characters"""
        characters = []
        
        # Search for characters from Baki series
        baki_queries = ["baki", "hanma", "yujiro", "jack", "retsu", "doppo", "katsumi", "pickle", "musashi"]
        
        for query in baki_queries:
            print(f"Searching for characters with query: {query}")
            data = self._make_request("characters", params={"q": query, "limit": 25})
            
            if data and "data" in data:
                for char_data in data["data"]:
                    # Filter for Baki-related characters
                    if self._is_baki_character(char_data):
                        character_info = {
                            "mal_id": char_data["mal_id"],
                            "name": char_data["name"],
                            "image_url": char_data["images"]["jpg"]["image_url"] if char_data.get("images") else None,
                            "anime_title": self._extract_anime_title(char_data)
                        }
                        
                        # Avoid duplicates
                        if not any(c["mal_id"] == character_info["mal_id"] for c in characters):
                            characters.append(character_info)
        
        return characters

    def download_image(self, image_url, character_name):
        """Download character image and save locally"""
        if not image_url:
            return None

        try:
            # Clean character name for filename
            safe_name = "".join(c for c in character_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_').lower()

            # Get file extension from URL
            parsed_url = urlparse(image_url)
            file_ext = os.path.splitext(parsed_url.path)[1] or '.jpg'

            filename = f"{safe_name}{file_ext}"
            filepath = os.path.join(self.images_dir, filename)

            # Skip if file already exists
            if os.path.exists(filepath):
                print(f"Image already exists: {filename}")
                return filepath

            # Download image
            print(f"Downloading image for {character_name}...")
            response = self.session.get(image_url, timeout=30)
            response.raise_for_status()

            # Save image
            with open(filepath, 'wb') as f:
                f.write(response.content)

            print(f"✅ Downloaded: {filename}")
            return filepath

        except Exception as e:
            print(f"❌ Failed to download image for {character_name}: {e}")
            return None

    def _is_baki_character(self, char_data):
        """Check if character is from Baki series"""
        name = char_data.get("name", "").lower()
        animeography = char_data.get("animeography", [])
        
        # Check if character name contains Baki-related terms
        baki_terms = ["baki", "hanma", "yujiro", "jack", "retsu", "doppo", "katsumi", "pickle", "musashi", "oliva", "sukune"]
        
        if any(term in name for term in baki_terms):
            return True
            
        # Check if character appears in Baki anime
        for anime in animeography:
            anime_name = anime.get("anime", {}).get("title", "").lower()
            if "baki" in anime_name or "grappler" in anime_name:
                return True
                
        return False
    
    def _extract_anime_title(self, char_data):
        """Extract the main anime title for the character"""
        animeography = char_data.get("animeography", [])
        
        for anime in animeography:
            anime_title = anime.get("anime", {}).get("title", "")
            if "baki" in anime_title.lower() or "grappler" in anime_title.lower():
                return anime_title
                
        return "Baki Series"
    
    def assign_rarity(self, characters):
        """Assign rarity to characters based on their importance/popularity"""
        rarities = list(RARITY_RATES.keys())
        rarity_weights = list(RARITY_RATES.values())
        
        for character in characters:
            # Assign rarity randomly based on weights
            character["rarity"] = random.choices(rarities, weights=rarity_weights)[0]
            
        return characters

def populate_character_database():
    """Populate the database with Baki characters from Jikan API"""
    # Initialize database first
    from database import init_database
    init_database()

    jikan = JikanAPI()
    db = SessionLocal()

    try:
        print("Fetching Baki characters from Jikan API...")
        characters = jikan.search_baki_characters()
        
        if not characters:
            print("No characters found!")
            return
            
        print(f"Found {len(characters)} characters")
        
        # Assign rarities
        characters = jikan.assign_rarity(characters)
        
        # Save to database
        for char_data in characters:
            # Check if character already exists
            existing = db.query(Character).filter_by(mal_id=char_data["mal_id"]).first()
            
            if not existing:
                character = Character(
                    mal_id=char_data["mal_id"],
                    name=char_data["name"],
                    image_url=char_data["image_url"],
                    rarity=char_data["rarity"],
                    anime_title=char_data["anime_title"]
                )
                db.add(character)
                print(f"Added character: {char_data['name']} ({char_data['rarity']})")
            else:
                print(f"Character already exists: {char_data['name']}")
        
        db.commit()
        print("Character database populated successfully!")
        
    except Exception as e:
        print(f"Error populating database: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    populate_character_database()
