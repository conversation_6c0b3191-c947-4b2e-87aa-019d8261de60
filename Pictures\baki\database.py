from sqlalchemy import create_engine, Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Index, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.pool import StaticPool
from datetime import datetime, timedelta
import config

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    discord_id = Column(String, unique=True, nullable=False, index=True)
    username = Column(String, nullable=False)
    user_type = Column(String, default='Player', index=True)
    currency = Column(Integer, default=config.STARTING_CURRENCY)
    last_daily_pull = Column(DateTime, nullable=True)
    pulls_today = Column(Integer, default=0)
    total_pulls = Column(Integer, default=0)
    start_date = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)

    # New leveling system fields
    level = Column(Integer, default=1)
    xp = Column(Integer, default=0)

    # Pull rate limiting fields
    pulls_remaining = Column(Integer, default=config.PULLS_PER_HOUR)
    last_pull_reset = Column(DateTime, default=datetime.utcnow)

    # Battle system
    battles_won = Column(Integer, default=0)
    battles_lost = Column(Integer, default=0)
    battle_streak = Column(Integer, default=0)
    last_battle_time = Column(DateTime)
    daily_battles = Column(Integer, default=0)
    last_battle_reset = Column(DateTime)

    # Relationships
    characters = relationship("UserCharacter", back_populates="user", lazy='dynamic')
    shards = relationship("CharacterShard", back_populates="user", lazy='dynamic')
    team_slots = relationship("TeamSlot", back_populates="user", lazy='dynamic')

class Character(Base):
    __tablename__ = 'characters'

    id = Column(Integer, primary_key=True)
    mal_id = Column(Integer, unique=True, nullable=False, index=True)
    name = Column(String, nullable=False, index=True)
    image_url = Column(Text, nullable=True)
    rarity = Column(String, nullable=False)
    anime_title = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user_characters = relationship("UserCharacter", back_populates="character", lazy='dynamic')
    shards = relationship("CharacterShard", back_populates="character", lazy='dynamic')

    # Composite indexes for common queries
    __table_args__ = (
        Index('idx_character_rarity_name', 'rarity', 'name'),
        Index('idx_character_name_lower', 'name'),
    )

class UserCharacter(Base):
    __tablename__ = 'user_characters'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    character_id = Column(Integer, ForeignKey('characters.id'), nullable=False, index=True)
    obtained_at = Column(DateTime, default=datetime.utcnow, index=True)
    is_favorite = Column(Boolean, default=False)

    # Character progression fields
    level = Column(Integer, default=1)
    xp = Column(Integer, default=0)
    tier = Column(Integer, default=0)

    # Relationships
    user = relationship("User", back_populates="characters")
    character = relationship("Character", back_populates="user_characters")
    team_slot = relationship("TeamSlot", back_populates="user_character", uselist=False)

    # Composite indexes and constraints for common queries and data integrity
    __table_args__ = (
        Index('idx_user_character', 'user_id', 'character_id'),
        Index('idx_user_obtained', 'user_id', 'obtained_at'),
        Index('idx_character_tier', 'character_id', 'tier'),
        UniqueConstraint('user_id', 'character_id', name='uq_user_character'),
    )

class PullHistory(Base):
    __tablename__ = 'pull_history'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    character_id = Column(Integer, ForeignKey('characters.id'), nullable=False)
    pull_type = Column(String, nullable=False)  # 'single', 'multi'
    pulled_at = Column(DateTime, default=datetime.utcnow)

class CharacterShard(Base):
    __tablename__ = 'character_shards'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    character_id = Column(Integer, ForeignKey('characters.id'), nullable=False, index=True)
    shard_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="shards")
    character = relationship("Character", back_populates="shards")

    # Composite indexes
    __table_args__ = (
        Index('idx_user_character_shard', 'user_id', 'character_id'),
    )

class TeamSlot(Base):
    __tablename__ = 'team_slots'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    slot_number = Column(Integer, nullable=False)  # 1-5
    user_character_id = Column(Integer, ForeignKey('user_characters.id'), nullable=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="team_slots")
    user_character = relationship("UserCharacter", back_populates="team_slot")

    # Composite indexes and constraints
    __table_args__ = (
        Index('idx_user_slot', 'user_id', 'slot_number'),
    )

class AIOpponent(Base):
    __tablename__ = 'ai_opponents'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    difficulty_level = Column(Integer, default=1)
    team_power = Column(Integer, default=0)
    wins = Column(Integer, default=0)
    losses = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    team_members = relationship("AIOpponentTeam", back_populates="ai_opponent")

class AIOpponentTeam(Base):
    __tablename__ = 'ai_opponent_teams'

    id = Column(Integer, primary_key=True)
    ai_opponent_id = Column(Integer, ForeignKey('ai_opponents.id'), nullable=False)
    character_id = Column(Integer, ForeignKey('characters.id'), nullable=False)
    slot_position = Column(Integer, nullable=False)  # 1-5
    character_level = Column(Integer, default=1)
    character_tier = Column(Integer, default=0)

    # Relationships
    ai_opponent = relationship("AIOpponent", back_populates="team_members")
    character = relationship("Character")

    # Ensure unique slot positions per AI opponent
    __table_args__ = (UniqueConstraint('ai_opponent_id', 'slot_position'),)

class BattleHistory(Base):
    __tablename__ = 'battle_history'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    opponent_type = Column(String(20), nullable=False)  # 'ai' or 'player'
    opponent_id = Column(Integer)  # ai_opponent_id or other user_id
    battle_result = Column(String(10), nullable=False)  # 'win', 'loss', 'draw'
    user_team_power = Column(Integer, default=0)
    opponent_team_power = Column(Integer, default=0)
    battle_duration = Column(Integer, default=0)  # in seconds
    xp_gained = Column(Integer, default=0)
    currency_gained = Column(Integer, default=0)
    battle_log = Column(Text)  # JSON string of battle events
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User")

class BattleReward(Base):
    __tablename__ = 'battle_rewards'

    id = Column(Integer, primary_key=True)
    battle_id = Column(Integer, ForeignKey('battle_history.id'), nullable=False)
    reward_type = Column(String(20), nullable=False)  # 'xp', 'currency', 'character', 'shard'
    reward_amount = Column(Integer, default=0)
    character_id = Column(Integer, ForeignKey('characters.id'))

    # Relationships
    battle = relationship("BattleHistory")
    character = relationship("Character")

# Database setup with optimizations
engine = create_engine(
    config.DATABASE_URL,
    echo=False,
    poolclass=StaticPool,
    connect_args={
        'check_same_thread': False,
        'timeout': 20,
        'isolation_level': None  # Autocommit mode for better performance
    },
    pool_pre_ping=True,
    pool_recycle=300
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Create all tables if they don't exist"""
    Base.metadata.create_all(bind=engine)

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize database with tables"""
    create_tables()
    print("Database initialized successfully!")
