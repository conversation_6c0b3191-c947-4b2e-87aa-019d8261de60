import discord
from discord.ext import commands
from discord import app_commands
from database import SessionLocal, User, Character, UserCharacter, PullHistory, CharacterShard
from bot import get_or_create_user
from cache import bot_cache
from datetime import datetime, timedelta
import random
import config
import os

class GachaCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def get_character_image_file(self, character):
        """Get local image file for character if it exists"""
        if not character.name:
            return None

        # Clean character name for filename
        safe_name = "".join(c for c in character.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_').lower()

        # Check for image file
        image_dir = "character_images"
        possible_extensions = ['.jpg', '.jpeg', '.png', '.gif']

        for ext in possible_extensions:
            filepath = os.path.join(image_dir, f"{safe_name}{ext}")
            if os.path.exists(filepath):
                return filepath

        return None
    
    def get_random_character(self):
        """Get a random character based on rarity rates using cache"""
        # Get rarity based on rates
        rarities = list(config.RARITY_RATES.keys())
        weights = list(config.RARITY_RATES.values())
        selected_rarity = random.choices(rarities, weights=weights)[0]

        # Get all characters of selected rarity from cache
        characters = bot_cache.get_characters_by_rarity(selected_rarity)

        if not characters:
            # Fallback to any character if no characters of selected rarity
            characters = bot_cache.get_all_characters()

        return random.choice(characters) if characters else None

    def check_pull_limits(self, user):
        """Check if user can pull (rate limiting)"""
        now = datetime.utcnow()

        # Reset pulls if an hour has passed
        if user.last_pull_reset and (now - user.last_pull_reset).total_seconds() >= 3600:
            user.pulls_remaining = config.PULLS_PER_HOUR
            user.last_pull_reset = now
        elif not user.last_pull_reset:
            user.last_pull_reset = now

        return user.pulls_remaining > 0

    def add_character_shard(self, db, user_id, character_id, shard_count=1):
        """Add shards for a character to user's inventory"""
        shard = db.query(CharacterShard).filter_by(
            user_id=user_id,
            character_id=character_id
        ).first()

        if shard:
            shard.shard_count += shard_count
            shard.updated_at = datetime.utcnow()
        else:
            shard = CharacterShard(
                user_id=user_id,
                character_id=character_id,
                shard_count=shard_count
            )
            db.add(shard)

        return shard

    def check_duplicate_character(self, db, user_id, character_id):
        """Check if user already owns this character"""
        existing = db.query(UserCharacter).filter_by(
            user_id=user_id,
            character_id=character_id
        ).first()
        return existing is not None

    def add_xp_to_character(self, db, user_character, xp_amount):
        """Add XP to a character and handle level ups"""
        # Ensure xp is not None
        if user_character.xp is None:
            user_character.xp = 0

        user_character.xp += xp_amount

        # Simple leveling: every 1000 XP = 1 level
        new_level = (user_character.xp // 1000) + 1
        if new_level > user_character.level:
            user_character.level = new_level
            return True  # Level up occurred
        return False

    def add_xp_to_player(self, db, user, xp_amount):
        """Add XP to player and handle level ups"""
        # Ensure xp is not None
        if user.xp is None:
            user.xp = 0

        user.xp += xp_amount

        # Player leveling: every 1000 XP = 1 level
        new_level = (user.xp // config.PLAYER_XP_PER_LEVEL) + 1
        if new_level > user.level:
            user.level = new_level
            return True  # Level up occurred
        return False
    
    @app_commands.command(name="pull", description="Pull for a random character")
    async def pull(self, interaction: discord.Interaction):
        """Pull for a random character"""
        # Immediately acknowledge the interaction
        await interaction.response.defer()

        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()

            if not user:
                await interaction.followup.send("You haven't started yet! Use `/start` to begin.")
                return

            # Check pull rate limits
            if not self.check_pull_limits(user):
                time_until_reset = 3600 - (datetime.utcnow() - user.last_pull_reset).total_seconds()
                minutes_left = int(time_until_reset // 60)
                await interaction.followup.send(
                    f"⏰ You've used all your pulls! Next reset in {minutes_left} minutes.\n"
                    f"Pulls: {user.pulls_remaining}/{config.PULLS_PER_HOUR}"
                )
                return

            # Check if user has enough currency
            if user.currency < config.PULL_COST:
                await interaction.followup.send(f"Not enough currency! You need {config.PULL_COST} coins but only have {user.currency}.")
                return

            # Get random character
            character = self.get_random_character()

            if not character:
                await interaction.response.send_message("No characters available! Please contact an administrator.")
                return

            # Deduct currency and pulls
            user.currency -= config.PULL_COST
            user.total_pulls += 1
            user.pulls_remaining -= 1

            # Check if this is a duplicate
            is_duplicate = self.check_duplicate_character(db, user.id, character.id)

            # Add XP to player
            player_leveled_up = self.add_xp_to_player(db, user, config.PLAYER_XP_PER_PULL)

            result_message = ""
            user_character = None

            if is_duplicate:
                # Give shards instead of duplicate character
                shard = self.add_character_shard(db, user.id, character.id, 1)
                result_message = f"🔸 **Duplicate!** You received 1 {character.name} shard!\nTotal shards: {shard.shard_count}"
            else:
                # Add new character to collection
                user_character = UserCharacter(
                    user_id=user.id,
                    character_id=character.id,
                    level=1,
                    xp=0,
                    tier=0,
                    obtained_at=datetime.utcnow(),
                    is_favorite=False
                )
                db.add(user_character)

                # Add XP to the new character
                char_leveled_up = self.add_xp_to_character(db, user_character, config.CHARACTER_XP_PER_PULL)
                result_message = f"✨ **New character acquired!**"
                if char_leveled_up:
                    result_message += f"\n🆙 {character.name} leveled up to Level {user_character.level}!"

            # Record pull history
            pull_record = PullHistory(
                user_id=user.id,
                character_id=character.id,
                pull_type='single'
            )
            db.add(pull_record)
            
            db.commit()

            # Create embed
            title = "🔸 Duplicate Character!" if is_duplicate else "✨ New Character!"
            nickname = getattr(character, 'nickname', 'Fighter')
            embed = discord.Embed(
                title=title,
                description=f"You pulled **{character.name}** `{nickname}`!\n{result_message}",
                color=config.RARITY_COLORS.get(character.rarity, 0x000000)
            )

            embed.add_field(
                name="Rarity",
                value=character.rarity,
                inline=True
            )

            embed.add_field(
                name="Series",
                value=character.anime_title or "Baki Series",
                inline=True
            )

            embed.add_field(
                name="Pulls Remaining",
                value=f"{user.pulls_remaining}/{config.PULLS_PER_HOUR}",
                inline=True
            )

            embed.add_field(
                name="Currency",
                value=f"{user.currency} coins",
                inline=True
            )

            embed.add_field(
                name="Player Level",
                value=f"Level {user.level} ({user.xp} XP)",
                inline=True
            )

            if user_character:
                embed.add_field(
                    name="Character Level",
                    value=f"Level {user_character.level} (Tier {user_character.tier})",
                    inline=True
                )

            if player_leveled_up:
                embed.add_field(
                    name="🎉 Level Up!",
                    value=f"You reached Player Level {user.level}!",
                    inline=False
                )

            embed.set_footer(text=f"Pull #{user.total_pulls}")

            # Check for local image file
            local_image = self.get_character_image_file(character)

            if local_image:
                # Send with local image as attachment
                file = discord.File(local_image, filename=f"{character.name.replace(' ', '_')}.jpg")
                embed.set_image(url=f"attachment://{file.filename}")
                await interaction.followup.send(embed=embed, file=file)
            else:
                # Fallback to URL if available
                if character.image_url:
                    embed.set_image(url=character.image_url)
                await interaction.followup.send(embed=embed)
            
        except Exception as e:
            print(f"Error in pull command: {e}")
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while pulling. Please try again.")
            else:
                await interaction.followup.send("An error occurred while pulling. Please try again.")
            db.rollback()
        finally:
            db.close()

    @app_commands.command(name="shards", description="View your character shards")
    async def shards(self, interaction: discord.Interaction):
        """View character shards inventory"""
        # Immediately acknowledge the interaction
        await interaction.response.defer()

        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()

            if not user:
                await interaction.followup.send("You haven't started yet! Use `/start` to begin.")
                return

            # Get all shards for the user
            shards = db.query(CharacterShard).filter_by(user_id=user.id).join(Character).all()

            if not shards:
                await interaction.response.send_message("🔸 You don't have any character shards yet! Pull duplicates to earn shards.")
                return

            # Sort shards by character rarity and shard count
            rarity_order = {'Mythical': 6, 'Legendary': 5, 'Epic': 4, 'Rare': 3, 'Uncommon': 2, 'Common': 1}
            shards.sort(key=lambda s: (rarity_order.get(s.character.rarity, 0), s.shard_count), reverse=True)

            embed = discord.Embed(
                title="🔸 Character Shards",
                description="Your collection of character fragments",
                color=0x00BFFF
            )

            shard_list = []
            total_shards = 0

            for shard in shards[:20]:  # Limit to 20 to avoid embed limits
                rarity_emoji = {
                    'Mythical': '🌟',
                    'Legendary': '🏆',
                    'Epic': '💜',
                    'Rare': '🔵',
                    'Uncommon': '🟢',
                    'Common': '⚪'
                }.get(shard.character.rarity, '❓')

                shard_list.append(f"{rarity_emoji} **{shard.character.name}** - {shard.shard_count} shards")
                total_shards += shard.shard_count

            embed.add_field(
                name="Shard Inventory",
                value="\n".join(shard_list) if shard_list else "No shards",
                inline=False
            )

            embed.add_field(
                name="Total Shards",
                value=f"{total_shards} shards",
                inline=True
            )

            if len(shards) > 20:
                embed.add_field(
                    name="Note",
                    value=f"Showing top 20 of {len(shards)} characters with shards",
                    inline=True
                )

            embed.set_footer(text="Use shards to upgrade character tiers!")

            await interaction.followup.send(embed=embed)

        except Exception as e:
            print(f"Error in shards command: {e}")
            await interaction.followup.send("An error occurred while fetching shards.")
        finally:
            db.close()



    @app_commands.command(name="inventory", description="View your character collection")
    async def inventory(self, interaction: discord.Interaction, page: int = 1):
        """Display user's character inventory"""
        # Immediately acknowledge the interaction
        await interaction.response.defer()

        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()

            if not user:
                await interaction.followup.send("You haven't started yet! Use `/start` to begin.")
                return
            
            # Get user's characters
            user_characters = db.query(UserCharacter).filter_by(user_id=user.id).join(Character).order_by(Character.rarity, Character.name).all()
            
            if not user_characters:
                await interaction.followup.send("Your inventory is empty! Use `/pull` to get your first character.")
                return
            
            # Pagination
            per_page = 10
            total_pages = (len(user_characters) + per_page - 1) // per_page
            page = max(1, min(page, total_pages))
            
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_characters = user_characters[start_idx:end_idx]
            
            embed = discord.Embed(
                title=f"{interaction.user.display_name}'s Inventory",
                description=f"Page {page}/{total_pages} • Total Characters: {len(user_characters)}",
                color=0x0099ff
            )
            
            for user_char in page_characters:
                character = user_char.character
                obtained_date = user_char.obtained_at.strftime("%Y-%m-%d")

                # Calculate character power
                char_power = (user_char.level * 100) + (user_char.tier * 500)

                rarity_emoji = {
                    'Mythical': '🌟',
                    'Legendary': '🏆',
                    'Epic': '💜',
                    'Rare': '🔵',
                    'Uncommon': '🟢',
                    'Common': '⚪'
                }.get(character.rarity, '❓')

                embed.add_field(
                    name=f"{rarity_emoji} {character.name}",
                    value=f"**{character.rarity}**\nLv.{user_char.level} T{user_char.tier}\nPower: {char_power:,}\nObtained: {obtained_date}",
                    inline=True
                )
            
            embed.set_footer(text=f"Use /inventory <page> to navigate pages")
            
            await interaction.response.send_message(embed=embed)
            
        finally:
            db.close()
    
    @app_commands.command(name="sacrifice", description="Sacrifice a character for currency")
    async def sacrifice(self, interaction: discord.Interaction, character_name: str):
        """Sacrifice a character for currency"""
        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            
            if not user:
                await interaction.response.send_message("You haven't started yet! Use `/start` to begin.")
                return
            
            # Find the character in user's inventory
            user_character = db.query(UserCharacter).join(Character).filter(
                UserCharacter.user_id == user.id,
                Character.name.ilike(f"%{character_name}%")
            ).first()
            
            if not user_character:
                await interaction.response.send_message(f"You don't have a character named '{character_name}' in your inventory.")
                return
            
            character = user_character.character
            
            # Calculate sacrifice value based on rarity
            sacrifice_values = {
                'Common': 50,
                'Uncommon': 100,
                'Rare': 200,
                'Epic': 500,
                'Legendary': 1000
            }
            
            currency_gained = sacrifice_values.get(character.rarity, 50)
            
            # Remove character from inventory
            db.delete(user_character)
            
            # Add currency to user
            user.currency += currency_gained
            
            db.commit()
            
            embed = discord.Embed(
                title="Character Sacrificed",
                description=f"You sacrificed **{character.name}** and gained **{currency_gained}** coins!",
                color=0xff6600
            )
            
            embed.add_field(
                name="Character Rarity",
                value=character.rarity,
                inline=True
            )
            
            embed.add_field(
                name="Currency Gained",
                value=f"{currency_gained} coins",
                inline=True
            )
            
            embed.add_field(
                name="Total Currency",
                value=f"{user.currency} coins",
                inline=True
            )
            
            await interaction.response.send_message(embed=embed)
            
        except Exception as e:
            print(f"Error in sacrifice command: {e}")
            await interaction.response.send_message("An error occurred while sacrificing the character.")
            db.rollback()
        finally:
            db.close()

async def setup(bot):
    await bot.add_cog(GachaCommands(bot))
