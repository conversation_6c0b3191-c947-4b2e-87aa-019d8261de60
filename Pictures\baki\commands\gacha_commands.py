import discord
from discord.ext import commands
from discord import app_commands
from database import SessionL<PERSON>al, <PERSON>r, Character, UserCharacter, PullHistory
from bot import get_or_create_user
from cache import bot_cache
from datetime import datetime, timedelta
import random
import config
import os

class GachaCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def get_character_image_file(self, character):
        """Get local image file for character if it exists"""
        if not character.name:
            return None

        # Clean character name for filename
        safe_name = "".join(c for c in character.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_').lower()

        # Check for image file
        image_dir = "character_images"
        possible_extensions = ['.jpg', '.jpeg', '.png', '.gif']

        for ext in possible_extensions:
            filepath = os.path.join(image_dir, f"{safe_name}{ext}")
            if os.path.exists(filepath):
                return filepath

        return None
    
    def get_random_character(self):
        """Get a random character based on rarity rates using cache"""
        # Get rarity based on rates
        rarities = list(config.RARITY_RATES.keys())
        weights = list(config.RARITY_RATES.values())
        selected_rarity = random.choices(rarities, weights=weights)[0]

        # Get all characters of selected rarity from cache
        characters = bot_cache.get_characters_by_rarity(selected_rarity)

        if not characters:
            # Fallback to any character if no characters of selected rarity
            characters = bot_cache.get_all_characters()

        return random.choice(characters) if characters else None
    
    @app_commands.command(name="pull", description="Pull for a random character")
    async def pull(self, interaction: discord.Interaction):
        """Pull for a random character"""
        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            
            if not user:
                await interaction.response.send_message("You haven't started yet! Use `/start` to begin.")
                return
            
            # Check if user has enough currency
            if user.currency < config.PULL_COST:
                await interaction.response.send_message(f"Not enough currency! You need {config.PULL_COST} coins but only have {user.currency}.")
                return
            
            # Get random character
            character = self.get_random_character()
            
            if not character:
                await interaction.response.send_message("No characters available! Please contact an administrator.")
                return
            
            # Deduct currency
            user.currency -= config.PULL_COST
            user.total_pulls += 1
            
            # Add character to user's collection
            user_character = UserCharacter(
                user_id=user.id,
                character_id=character.id
            )
            db.add(user_character)
            
            # Record pull history
            pull_record = PullHistory(
                user_id=user.id,
                character_id=character.id,
                pull_type='single'
            )
            db.add(pull_record)
            
            db.commit()
            
            # Create embed
            embed = discord.Embed(
                title="Character Pulled!",
                description=f"You pulled **{character.name}**!",
                color=config.RARITY_COLORS.get(character.rarity, 0x000000)
            )
            
            embed.add_field(
                name="Rarity",
                value=character.rarity,
                inline=True
            )
            
            embed.add_field(
                name="Series",
                value=character.anime_title or "Baki Series",
                inline=True
            )
            
            embed.add_field(
                name="Remaining Currency",
                value=f"{user.currency} coins",
                inline=True
            )
            
            embed.set_footer(text=f"Pull #{user.total_pulls}")

            # Check for local image file
            local_image = self.get_character_image_file(character)

            if local_image:
                # Send with local image as attachment
                file = discord.File(local_image, filename=f"{character.name.replace(' ', '_')}.jpg")
                embed.set_image(url=f"attachment://{file.filename}")
                await interaction.response.send_message(embed=embed, file=file)
            else:
                # Fallback to URL if available
                if character.image_url:
                    embed.set_image(url=character.image_url)
                await interaction.response.send_message(embed=embed)
            
        except Exception as e:
            print(f"Error in pull command: {e}")
            await interaction.response.send_message("An error occurred while pulling. Please try again.")
            db.rollback()
        finally:
            db.close()
    
    @app_commands.command(name="inventory", description="View your character collection")
    async def inventory(self, interaction: discord.Interaction, page: int = 1):
        """Display user's character inventory"""
        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            
            if not user:
                await interaction.response.send_message("You haven't started yet! Use `/start` to begin.")
                return
            
            # Get user's characters
            user_characters = db.query(UserCharacter).filter_by(user_id=user.id).join(Character).order_by(Character.rarity, Character.name).all()
            
            if not user_characters:
                await interaction.response.send_message("Your inventory is empty! Use `/pull` to get your first character.")
                return
            
            # Pagination
            per_page = 10
            total_pages = (len(user_characters) + per_page - 1) // per_page
            page = max(1, min(page, total_pages))
            
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_characters = user_characters[start_idx:end_idx]
            
            embed = discord.Embed(
                title=f"{interaction.user.display_name}'s Inventory",
                description=f"Page {page}/{total_pages} • Total Characters: {len(user_characters)}",
                color=0x0099ff
            )
            
            for user_char in page_characters:
                character = user_char.character
                obtained_date = user_char.obtained_at.strftime("%Y-%m-%d")
                
                embed.add_field(
                    name=f"{character.name}",
                    value=f"**{character.rarity}**\n{character.anime_title or 'Baki Series'}\nObtained: {obtained_date}",
                    inline=True
                )
            
            embed.set_footer(text=f"Use /inventory <page> to navigate pages")
            
            await interaction.response.send_message(embed=embed)
            
        finally:
            db.close()
    
    @app_commands.command(name="sacrifice", description="Sacrifice a character for currency")
    async def sacrifice(self, interaction: discord.Interaction, character_name: str):
        """Sacrifice a character for currency"""
        db = SessionLocal()
        try:
            user = db.query(User).filter_by(discord_id=str(interaction.user.id)).first()
            
            if not user:
                await interaction.response.send_message("You haven't started yet! Use `/start` to begin.")
                return
            
            # Find the character in user's inventory
            user_character = db.query(UserCharacter).join(Character).filter(
                UserCharacter.user_id == user.id,
                Character.name.ilike(f"%{character_name}%")
            ).first()
            
            if not user_character:
                await interaction.response.send_message(f"You don't have a character named '{character_name}' in your inventory.")
                return
            
            character = user_character.character
            
            # Calculate sacrifice value based on rarity
            sacrifice_values = {
                'Common': 50,
                'Uncommon': 100,
                'Rare': 200,
                'Epic': 500,
                'Legendary': 1000
            }
            
            currency_gained = sacrifice_values.get(character.rarity, 50)
            
            # Remove character from inventory
            db.delete(user_character)
            
            # Add currency to user
            user.currency += currency_gained
            
            db.commit()
            
            embed = discord.Embed(
                title="Character Sacrificed",
                description=f"You sacrificed **{character.name}** and gained **{currency_gained}** coins!",
                color=0xff6600
            )
            
            embed.add_field(
                name="Character Rarity",
                value=character.rarity,
                inline=True
            )
            
            embed.add_field(
                name="Currency Gained",
                value=f"{currency_gained} coins",
                inline=True
            )
            
            embed.add_field(
                name="Total Currency",
                value=f"{user.currency} coins",
                inline=True
            )
            
            await interaction.response.send_message(embed=embed)
            
        except Exception as e:
            print(f"Error in sacrifice command: {e}")
            await interaction.response.send_message("An error occurred while sacrificing the character.")
            db.rollback()
        finally:
            db.close()

async def setup(bot):
    await bot.add_cog(GachaCommands(bot))
