import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Discord Configuration
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
CLIENT_ID = os.getenv('CLIENT_ID')
GUILD_ID = os.getenv('GUILD_ID')

# Database Configuration
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///baki_gacha.db')

# Bot Configuration
COMMAND_PREFIX = os.getenv('COMMAND_PREFIX', '/')
DAILY_PULL_COOLDOWN = int(os.getenv('DAILY_PULL_COOLDOWN', 86400))
PULL_COST = int(os.getenv('PULL_COST', 100))
STARTING_CURRENCY = int(os.getenv('STARTING_CURRENCY', 1000))
MAX_PULLS_PER_DAY = int(os.getenv('MAX_PULLS_PER_DAY', 10))

# Gacha Configuration
RARITY_RATES = {
    'Common': 0.60,      # 60%
    'Uncommon': 0.25,    # 25%
    'Rare': 0.10,        # 10%
    'Epic': 0.04,        # 4%
    'Legendary': 0.01    # 1%
}

RARITY_COLORS = {
    'Common': 0x808080,      # Gray
    'Uncommon': 0x00FF00,    # Green
    'Rare': 0x0080FF,        # Blue
    'Epic': 0x8000FF,        # Purple
    'Legendary': 0xFFD700    # Gold
}

# Jikan API Configuration
JIKAN_BASE_URL = "https://api.jikan.moe/v4"
BAKI_ANIME_ID = 287  # Baki anime ID on MyAnimeList
