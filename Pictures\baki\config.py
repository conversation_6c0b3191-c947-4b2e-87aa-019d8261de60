import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Discord Configuration
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
CLIENT_ID = os.getenv('CLIENT_ID')
GUILD_ID = os.getenv('GUILD_ID')

# Database Configuration
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///baki_gacha.db')

# Bot Configuration
COMMAND_PREFIX = os.getenv('COMMAND_PREFIX', '/')
DAILY_PULL_COOLDOWN = int(os.getenv('DAILY_PULL_COOLDOWN', 86400))
PULL_COST = int(os.getenv('PULL_COST', 100))
STARTING_CURRENCY = int(os.getenv('STARTING_CURRENCY', 1000))
MAX_PULLS_PER_DAY = int(os.getenv('MAX_PULLS_PER_DAY', 10))

# Gacha Configuration - Expanded 8-tier system
RARITY_RATES = {
    'Common': 0.40,        # 40%
    'Uncommon': 0.25,      # 25%
    'Rare': 0.15,          # 15%
    'Epic': 0.10,          # 10%
    'Legendary': 0.06,     # 6%
    'Mythical': 0.03,      # 3%
    'Transcendent': 0.008, # 0.8%
    'Divine': 0.002        # 0.2%
}

RARITY_COLORS = {
    'Common': 0x808080,        # Gray
    'Uncommon': 0x00FF00,      # Green
    'Rare': 0x0080FF,          # Blue
    'Epic': 0x8000FF,          # Purple
    'Legendary': 0xFFD700,     # Gold
    'Mythical': 0xFF0080,      # Pink/Magenta
    'Transcendent': 0x00FFFF,  # Cyan
    'Divine': 0xFFFFFF         # White
}

# Character Tier Progression (shards needed for each tier upgrade)
TIER_SHARD_REQUIREMENTS = {
    0: 1,   # T0 -> T1: 1 shard
    1: 3,   # T1 -> T2: 3 shards
    2: 6,   # T2 -> T3: 6 shards
    3: 10,  # T3 -> T4: 10 shards
    4: 15,  # T4 -> T5: 15 shards
    5: 21,  # T5 -> T6: 21 shards
    6: 28,  # T6 -> T7: 28 shards
    7: 36,  # T7 -> T8: 36 shards
    8: 45,  # T8 -> T9: 45 shards
    9: 55   # T9 -> T10: 55 shards
}

# Power Scaling Configuration (prevents snowballing)
RARITY_POWER_RANGES = {
    'Common': {'base': (70, 90), 'growth': (8, 12)},
    'Uncommon': {'base': (100, 130), 'growth': (12, 18)},
    'Rare': {'base': (140, 180), 'growth': (18, 25)},
    'Epic': {'base': (190, 250), 'growth': (25, 32)},
    'Legendary': {'base': (260, 320), 'growth': (30, 38)},
    'Mythical': {'base': (330, 420), 'growth': (35, 50)},
    'Transcendent': {'base': (430, 500), 'growth': (40, 55)},
    'Divine': {'base': (510, 600), 'growth': (45, 60)}
}

# Power scaling diminishing returns (prevents late-game snowballing)
POWER_SCALING_FACTORS = {
    'level_cap_soft': 50,      # Soft cap where growth starts diminishing
    'level_cap_hard': 100,     # Hard cap where growth becomes minimal
    'diminishing_factor': 0.8, # Multiplier after soft cap
    'hard_cap_factor': 0.3     # Multiplier after hard cap
}

# XP Configuration
CHARACTER_XP_PER_PULL = 100  # XP gained when pulling a character
PLAYER_XP_PER_PULL = 50      # XP gained by player per pull
PLAYER_XP_PER_LEVEL = 1000   # XP needed for player to level up
XP_PER_LEVEL = 1000          # XP needed for character to level up

# Pull Rate Limiting
PULLS_PER_HOUR = 30
PULL_COOLDOWN_SECONDS = 120  # 2 minutes between pulls

# Jikan API Configuration
JIKAN_BASE_URL = "https://api.jikan.moe/v4"
BAKI_ANIME_ID = 287  # Baki anime ID on MyAnimeList
