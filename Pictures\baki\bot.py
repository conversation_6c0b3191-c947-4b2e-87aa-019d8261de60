import discord
from discord.ext import commands
import asyncio
import config
from database import init_database, SessionLocal, User
from jikan_api import populate_character_database

class BakiGachaBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        
        super().__init__(
            command_prefix=config.COMMAND_PREFIX,
            intents=intents,
            help_command=None
        )
        
    async def setup_hook(self):
        """Called when the bot is starting up"""
        print("Setting up bot...")
        
        # Initialize database
        init_database()
        
        # Load cogs
        await self.load_cogs()
        
        # Sync commands
        if config.GUILD_ID:
            guild = discord.Object(id=int(config.GUILD_ID))
            self.tree.copy_global_to(guild=guild)
            await self.tree.sync(guild=guild)
            print(f"Commands synced to guild {config.GUILD_ID}")
        else:
            await self.tree.sync()
            print("Commands synced globally")
    
    async def load_cogs(self):
        """Load all command cogs"""
        cogs = [
            'commands.user_commands',
            'commands.gacha_commands',
            'commands.info_commands'
        ]
        
        for cog in cogs:
            try:
                await self.load_extension(cog)
                print(f"Loaded cog: {cog}")
            except Exception as e:
                print(f"Failed to load cog {cog}: {e}")
    
    async def on_ready(self):
        """Called when bot is ready"""
        print(f"{self.user} has connected to Discord!")
        print(f"Bot is in {len(self.guilds)} guilds")
        
        # Set bot status
        activity = discord.Game(name="Baki Gacha | /help")
        await self.change_presence(activity=activity)
    
    async def on_command_error(self, ctx, error):
        """Handle command errors"""
        if isinstance(error, commands.CommandNotFound):
            return
        elif isinstance(error, commands.MissingRequiredArgument):
            await ctx.send(f"Missing required argument: {error.param}")
        elif isinstance(error, commands.BadArgument):
            await ctx.send(f"Invalid argument: {error}")
        else:
            print(f"Unhandled error: {error}")
            await ctx.send("An error occurred while processing the command.")

def get_or_create_user(discord_id: str, username: str):
    """Get existing user or create new one"""
    db = SessionLocal()
    try:
        user = db.query(User).filter_by(discord_id=discord_id).first()
        
        if not user:
            user = User(
                discord_id=discord_id,
                username=username,
                currency=config.STARTING_CURRENCY
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            print(f"Created new user: {username}")
        
        return user
    finally:
        db.close()

async def main():
    """Main function to run the bot"""
    bot = BakiGachaBot()
    
    try:
        await bot.start(config.DISCORD_TOKEN)
    except KeyboardInterrupt:
        print("Bot stopped by user")
    except Exception as e:
        print(f"Error running bot: {e}")
    finally:
        await bot.close()

if __name__ == "__main__":
    # Populate character database if empty
    db = SessionLocal()
    from database import Character
    character_count = db.query(Character).count()
    db.close()
    
    if character_count == 0:
        print("Character database is empty. Populating with Jikan API data...")
        populate_character_database()
    
    # Run the bot
    asyncio.run(main())
