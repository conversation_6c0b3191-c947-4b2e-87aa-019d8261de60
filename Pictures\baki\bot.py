import discord
from discord.ext import commands
import asyncio
import config
from database import init_database, SessionLocal, User
from jikan_api import populate_character_database
from cache import bot_cache

class BakiGachaBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True

        super().__init__(
            command_prefix=config.COMMAND_PREFIX,
            intents=intents,
            help_command=None,
            max_messages=None,  # Disable message cache for memory efficiency
            chunk_guilds_at_startup=False,  # Don't chunk all guilds at startup
            member_cache_flags=discord.MemberCacheFlags.none()  # Minimal member caching
        )
        
    async def setup_hook(self):
        """Called when the bot is starting up"""
        print("Setting up bot...")

        # Initialize database
        init_database()

        # Load character cache asynchronously
        print("Loading character cache...")
        await asyncio.get_event_loop().run_in_executor(None, bot_cache.load_characters)
        print("Cache loaded successfully!")

        # Load cogs
        await self.load_cogs()

        # Sync commands
        if config.GUILD_ID:
            guild = discord.Object(id=int(config.GUILD_ID))
            self.tree.copy_global_to(guild=guild)
            await self.tree.sync(guild=guild)
            print(f"Commands synced to guild {config.GUILD_ID}")
        else:
            await self.tree.sync()
            print("Commands synced globally")

    async def on_ready(self):
        """Called when the bot successfully connects to Discord"""
        print(f'✅ {self.user} has connected to Discord!')
        print(f'📊 Bot is in {len(self.guilds)} guilds')

        # Check permissions in each guild
        for guild in self.guilds:
            print(f"\n🏰 Guild: {guild.name} (ID: {guild.id})")

            # Get bot member in guild
            bot_member = guild.get_member(self.user.id)
            if bot_member:
                perms = bot_member.guild_permissions

                # Check critical permissions
                critical_perms = {
                    'send_messages': perms.send_messages,
                    'use_slash_commands': perms.use_slash_commands,
                    'embed_links': perms.embed_links,
                    'read_message_history': perms.read_message_history,
                    'add_reactions': perms.add_reactions,
                    'view_channel': perms.view_channel
                }

                print("🔐 Permissions Status:")
                for perm_name, has_perm in critical_perms.items():
                    status = "✅" if has_perm else "❌"
                    print(f"  {status} {perm_name.replace('_', ' ').title()}")

                # Check if missing any critical permissions
                missing_perms = [name for name, has_perm in critical_perms.items() if not has_perm]
                if missing_perms:
                    print(f"⚠️  Missing permissions: {', '.join(missing_perms)}")
                    print("🔗 Re-invite with proper permissions using:")
                    print("   python generate_invite.py")
                else:
                    print("✅ All required permissions granted!")
            else:
                print("❌ Bot member not found in guild")

        if not self.guilds:
            print("⚠️  Bot is not in any guilds!")
            print("🔗 Invite the bot using: python generate_invite.py")
    
    async def load_cogs(self):
        """Load all command cogs efficiently"""
        cogs = [
            'commands.user_commands',
            'commands.gacha_commands',
            'commands.info_commands',
            'commands.team_commands',
            'commands.brawl_commands'
        ]

        # Load cogs concurrently for faster startup
        tasks = []
        for cog in cogs:
            tasks.append(self._load_single_cog(cog))

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _load_single_cog(self, cog_name: str):
        """Load a single cog with error handling"""
        try:
            await self.load_extension(cog_name)
            print(f"Loaded cog: {cog_name}")
        except Exception as e:
            print(f"Failed to load cog {cog_name}: {e}")
    
    async def on_ready(self):
        """Called when bot is ready"""
        print(f"{self.user} has connected to Discord!")
        print(f"Bot is in {len(self.guilds)} guilds")
        
        # Set bot status
        activity = discord.Game(name="Baki Gacha | /help")
        await self.change_presence(activity=activity)
    
    async def on_command_error(self, ctx, error):
        """Handle command errors"""
        if isinstance(error, commands.CommandNotFound):
            return
        elif isinstance(error, commands.MissingRequiredArgument):
            await ctx.send(f"Missing required argument: {error.param}")
        elif isinstance(error, commands.BadArgument):
            await ctx.send(f"Invalid argument: {error}")
        else:
            print(f"Unhandled error: {error}")
            await ctx.send("An error occurred while processing the command.")

def get_or_create_user(discord_id: str, username: str, user_type: str = "Player"):
    """Get existing user or create new one with caching"""
    # Check cache first
    cached_user = bot_cache.get_cached_user(discord_id)
    if cached_user:
        return cached_user

    db = SessionLocal()
    try:
        user = db.query(User).filter_by(discord_id=discord_id).first()

        if not user:
            user = User(
                discord_id=discord_id,
                username=username,
                user_type=user_type,
                currency=config.STARTING_CURRENCY
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            print(f"Created new user: {username} ({user_type})")

        # Cache the user
        bot_cache.cache_user(user)
        return user
    finally:
        db.close()

async def main():
    """Main function to run the bot"""
    bot = BakiGachaBot()

    try:
        print("🚀 Starting Baki Gacha Bot...")
        await bot.start(config.DISCORD_TOKEN)
    except discord.LoginFailure:
        print("❌ Invalid bot token! Check your DISCORD_TOKEN in .env file")
    except discord.PrivilegedIntentsRequired:
        print("❌ Missing privileged intents! Enable them in Discord Developer Portal")
    except discord.HTTPException as e:
        if e.status == 403:
            print("❌ 403 Forbidden - Bot token is valid but missing server access")
            print("🔗 Invite the bot using: python generate_invite.py")
        else:
            print(f"❌ HTTP Error {e.status}: {e}")
    except KeyboardInterrupt:
        print("🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if not bot.is_closed():
            await bot.close()

if __name__ == "__main__":
    # Populate character database if empty
    db = SessionLocal()
    from database import Character
    character_count = db.query(Character).count()
    db.close()
    
    if character_count == 0:
        print("Character database is empty. Populating with Jikan API data...")
        populate_character_database()
    
    # Run the bot
    asyncio.run(main())
