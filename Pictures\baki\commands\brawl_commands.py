#!/usr/bin/env python3
"""
Brawl Commands for Baki Gacha Bot
Handles PvP brawls, AI brawls, and brawl history
"""

import discord
from discord.ext import commands
from discord import app_commands
from database import SessionLocal, User, UserCharacter, Character, TeamSlot, AIOpponent, AIOpponentTeam, BattleHistory
from battle_engine import BattleEngine, BattleCharacter
from ai_opponent_generator import AIOpponentGenerator
import json
import random
from datetime import datetime, timedelta
import config

class BrawlModeSelect(discord.ui.Select):
    def __init__(self):
        options = [
            discord.SelectOption(
                label="🤖 AI Opponent",
                value="ai",
                description="Fight against an AI opponent with random team",
                emoji="🤖"
            ),
            discord.SelectOption(
                label="⚔️ Player vs Player",
                value="pvp",
                description="Challenge another player to a brawl",
                emoji="⚔️"
            ),
            discord.SelectOption(
                label="🏆 Tournament Mode",
                value="tournament",
                description="Face increasingly difficult AI opponents",
                emoji="🏆"
            ),
            discord.SelectOption(
                label="💀 Death Match",
                value="deathmatch",
                description="High stakes - winner takes character shards",
                emoji="💀"
            ),
            discord.SelectOption(
                label="🎯 Training Mode",
                value="training",
                description="Practice against weaker opponents for XP",
                emoji="🎯"
            )
        ]
        super().__init__(placeholder="Choose your brawl mode...", options=options)

    async def callback(self, interaction: discord.Interaction):
        mode = self.values[0]

        if mode == "ai":
            await self.start_ai_brawl(interaction)
        elif mode == "pvp":
            await self.start_pvp_setup(interaction)
        elif mode == "tournament":
            await self.start_tournament(interaction)
        elif mode == "deathmatch":
            await self.start_deathmatch(interaction)
        elif mode == "training":
            await self.start_training(interaction)

    async def start_ai_brawl(self, interaction):
        """Start AI brawl mode - use existing AI brawl system"""
        # Redirect to existing AI brawl functionality
        brawl_cog = interaction.client.get_cog('BrawlCommands')
        if brawl_cog:
            await brawl_cog.ai_brawl_internal(interaction)

    async def start_pvp_setup(self, interaction):
        """Start PvP setup"""
        await interaction.response.send_message(
            "🔍 **Player vs Player Setup**\n"
            "Use `/challenge @player` to challenge another player to a brawl!\n"
            "Or wait for someone to challenge you.",
            ephemeral=True
        )

    async def start_tournament(self, interaction):
        """Start tournament mode"""
        await interaction.response.send_message(
            "🏆 **Tournament Mode**\n"
            "Coming soon! Face increasingly difficult opponents for big rewards.",
            ephemeral=True
        )

    async def start_deathmatch(self, interaction):
        """Start death match mode"""
        await interaction.response.send_message(
            "💀 **Death Match Mode**\n"
            "High stakes brawling! Winner takes character shards from loser.\n"
            "Use `/deathmatch @player` to challenge someone to a death match!\n"
            "⚠️ **Warning**: You can lose shards in this mode!",
            ephemeral=True
        )

    async def start_training(self, interaction):
        """Start training mode"""
        await interaction.response.send_message(
            "🎯 **Training Mode**\n"
            "Coming soon! Practice against weaker opponents for extra XP.",
            ephemeral=True
        )

class BrawlModeView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=300)
        self.add_item(BrawlModeSelect())

class BrawlView(discord.ui.View):
    """Interactive view for brawl results and replay"""

    def __init__(self, brawl_result, user_team, opponent_team, opponent_name):
        super().__init__(timeout=300)
        self.brawl_result = brawl_result
        self.user_team = user_team
        self.opponent_team = opponent_team
        self.opponent_name = opponent_name
        self.current_page = 0
        self.events_per_page = 5
    
    @discord.ui.button(label="📊 Brawl Summary", style=discord.ButtonStyle.primary)
    async def brawl_summary(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Show brawl summary"""
        embed = discord.Embed(
            title="🥊 Brawl Summary",
            color=0x00ff00 if self.brawl_result['winner'] == 'team1' else 0xff0000
        )

        # Winner info
        winner_text = "🏆 **VICTORY!**" if self.brawl_result['winner'] == 'team1' else "💀 **DEFEAT**"
        if self.brawl_result['winner'] == 'draw':
            winner_text = "🤝 **DRAW**"

        embed.add_field(
            name="Result",
            value=f"{winner_text}\n{self.brawl_result['reason']}",
            inline=False
        )

        # Brawl stats
        embed.add_field(
            name="Brawl Stats",
            value=f"**Turns:** {self.brawl_result['turns']}\n"
                  f"**Duration:** {self.brawl_result['duration']} seconds\n"
                  f"**Your Team Power:** {self.brawl_result['team1_power']:,}\n"
                  f"**{self.opponent_name} Power:** {self.brawl_result['team2_power']:,}",
            inline=True
        )
        
        # Team comparison
        user_team_text = "\n".join([f"• {char.name} (Lv.{char.level})" for char in self.user_team if char])
        opponent_team_text = "\n".join([f"• {char.name} (Lv.{char.level})" for char in self.opponent_team if char])
        
        embed.add_field(
            name="Your Team",
            value=user_team_text or "No team",
            inline=True
        )
        
        embed.add_field(
            name=f"{self.opponent_name} Team",
            value=opponent_team_text or "No team",
            inline=True
        )
        
        await interaction.response.edit_message(embed=embed, view=self)
    
    @discord.ui.button(label="📜 Brawl Log", style=discord.ButtonStyle.secondary)
    async def brawl_log(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Show paginated brawl log"""
        brawl_events = self.brawl_result['battle_log']

        if not brawl_events:
            embed = discord.Embed(
                title="📜 Brawl Log",
                description="No brawl events recorded.",
                color=0x808080
            )
            await interaction.response.edit_message(embed=embed, view=self)
            return

        # Calculate pagination
        total_pages = (len(brawl_events) + self.events_per_page - 1) // self.events_per_page
        start_idx = self.current_page * self.events_per_page
        end_idx = min(start_idx + self.events_per_page, len(brawl_events))

        embed = discord.Embed(
            title=f"📜 Brawl Log (Page {self.current_page + 1}/{total_pages})",
            color=0x0099ff
        )
        
        log_text = ""
        for i in range(start_idx, end_idx):
            event = brawl_events[i]
            log_text += f"**Turn {event['turn']}:** {event['description']}\n"

        embed.description = log_text

        # Update button states
        self.previous_page.disabled = self.current_page == 0
        self.next_page.disabled = self.current_page >= total_pages - 1

        await interaction.response.edit_message(embed=embed, view=self)
    
    @discord.ui.button(label="◀️", style=discord.ButtonStyle.secondary)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go to previous page of brawl log"""
        if self.current_page > 0:
            self.current_page -= 1
            await self.brawl_log(interaction, button)

    @discord.ui.button(label="▶️", style=discord.ButtonStyle.secondary)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go to next page of brawl log"""
        brawl_events = self.brawl_result['battle_log']
        total_pages = (len(brawl_events) + self.events_per_page - 1) // self.events_per_page

        if self.current_page < total_pages - 1:
            self.current_page += 1
            await self.brawl_log(interaction, button)

class BrawlCommands(commands.Cog):
    """Brawl system commands"""

    def __init__(self, bot):
        self.bot = bot
        self.battle_engine = BattleEngine()
        self.ai_generator = AIOpponentGenerator()

    @app_commands.command(name="brawl", description="Challenge an AI opponent to brawl!")
    @app_commands.describe(
        difficulty="Choose AI difficulty (1=Easy, 6=Legendary)"
    )
    @app_commands.choices(difficulty=[
        app_commands.Choice(name="🟢 Easy (Level 1-2)", value=1),
        app_commands.Choice(name="🟡 Medium (Level 3-4)", value=2),
        app_commands.Choice(name="🟠 Hard (Level 5-6)", value=3),
        app_commands.Choice(name="🔴 Random", value=0)
    ])
    async def brawl(self, interaction: discord.Interaction, difficulty: int = 0):
        """Start a brawl against an AI opponent"""
        
        await interaction.response.defer()
        
        db = SessionLocal()
        try:
            # Get or create user
            user = db.query(User).filter_by(discord_id=interaction.user.id).first()
            if not user:
                embed = discord.Embed(
                    title="❌ Not Registered",
                    description="Use `/start` to register first!",
                    color=0xff0000
                )
                await interaction.followup.send(embed=embed)
                return
            
            # No daily limits for brawls - unlimited brawling!
            now = datetime.utcnow()
            
            # Get user's team
            user_team_slots = db.query(TeamSlot).filter_by(user_id=user.id).order_by(TeamSlot.slot_number).all()
            
            if not any(slot.user_character_id for slot in user_team_slots):
                embed = discord.Embed(
                    title="❌ No Team Set",
                    description="Set up your team with `/team` first!",
                    color=0xff0000
                )
                await interaction.followup.send(embed=embed)
                return
            
            # Build user team for battle
            user_battle_team = []
            for slot in user_team_slots:
                if slot.user_character_id:
                    user_char = db.query(UserCharacter).filter_by(id=slot.user_character_id).first()
                    character = db.query(Character).filter_by(id=user_char.character_id).first()
                    battle_char = BattleCharacter.from_user_character(user_char, character)
                    user_battle_team.append(battle_char)
                else:
                    user_battle_team.append(None)
            
            # Get AI opponent based on difficulty
            if difficulty == 0:
                # Random opponent based on user level
                ai_opponent_id = self.ai_generator.get_random_ai_opponent(user.level)
            else:
                # Get opponents in difficulty range
                if difficulty == 1:
                    difficulty_levels = [1, 2]
                elif difficulty == 2:
                    difficulty_levels = [3, 4]
                else:  # difficulty == 3
                    difficulty_levels = [5, 6]
                
                ai_opponents = db.query(AIOpponent).filter(
                    AIOpponent.difficulty_level.in_(difficulty_levels)
                ).all()
                
                if ai_opponents:
                    ai_opponent = random.choice(ai_opponents)
                    ai_opponent_id = ai_opponent.id
                else:
                    ai_opponent_id = self.ai_generator.get_random_ai_opponent(user.level)
            
            if not ai_opponent_id:
                embed = discord.Embed(
                    title="❌ No AI Opponents Available",
                    description="No AI opponents found. Contact an admin.",
                    color=0xff0000
                )
                await interaction.followup.send(embed=embed)
                return
            
            # Get AI opponent and team
            ai_opponent = db.query(AIOpponent).filter_by(id=ai_opponent_id).first()
            ai_team_members = db.query(AIOpponentTeam).filter_by(
                ai_opponent_id=ai_opponent_id
            ).order_by(AIOpponentTeam.slot_position).all()
            
            # Build AI team for battle
            ai_battle_team = []
            for member in ai_team_members:
                character = db.query(Character).filter_by(id=member.character_id).first()
                battle_char = BattleCharacter.from_ai_character(member, character)
                ai_battle_team.append(battle_char)
            
            # Simulate battle
            battle_result = self.battle_engine.simulate_battle(user_battle_team, ai_battle_team)
            
            # Determine rewards and update stats
            xp_gained = 0
            if battle_result['winner'] == 'team1':
                # Victory
                user.battles_won += 1
                user.battle_streak += 1
                ai_opponent.losses += 1
                xp_gained = 50 + (ai_opponent.difficulty_level * 10)
            elif battle_result['winner'] == 'team2':
                # Defeat
                user.battles_lost += 1
                user.battle_streak = 0
                ai_opponent.wins += 1
                xp_gained = 20 + (ai_opponent.difficulty_level * 5)
            else:
                # Draw
                user.battle_streak = 0
                xp_gained = 30
            
            # Add XP to user
            user.xp += xp_gained
            
            # Check for level up
            level_up = False
            while user.xp >= user.level * 1000:
                user.xp -= user.level * 1000
                user.level += 1
                level_up = True
            
            # Update brawl tracking
            user.last_battle_time = now
            
            # Save battle history
            battle_history = BattleHistory(
                user_id=user.id,
                opponent_type='ai',
                opponent_id=ai_opponent_id,
                battle_result=battle_result['winner'].replace('team1', 'win').replace('team2', 'loss').replace('draw', 'draw'),
                user_team_power=battle_result['team1_power'],
                opponent_team_power=battle_result['team2_power'],
                battle_duration=battle_result['duration'],
                xp_gained=xp_gained,
                battle_log=json.dumps(battle_result['battle_log'])
            )
            db.add(battle_history)
            
            db.commit()
            
            # Create brawl result embed
            color = 0x00ff00 if battle_result['winner'] == 'team1' else 0xff0000
            if battle_result['winner'] == 'draw':
                color = 0xffff00

            embed = discord.Embed(
                title=f"🥊 Brawl vs {ai_opponent.name}",
                description=battle_result['reason'],
                color=color
            )

            embed.add_field(
                name="Rewards",
                value=f"**XP Gained:** +{xp_gained}\n" +
                      (f"🎉 **LEVEL UP!** You are now level {user.level}!" if level_up else ""),
                inline=False
            )

            embed.add_field(
                name="Brawl Stats",
                value=f"**Brawls Won:** {user.battles_won}\n"
                      f"**Brawls Lost:** {user.battles_lost}\n"
                      f"**Current Streak:** {user.battle_streak}",
                inline=True
            )

            # Create interactive view
            view = BrawlView(battle_result, user_battle_team, ai_battle_team, ai_opponent.name)
            
            await interaction.followup.send(embed=embed, view=view)
            
        except Exception as e:
            print(f"❌ Error in brawl command: {e}")
            embed = discord.Embed(
                title="❌ Brawl Error",
                description="An error occurred during brawl. Please try again.",
                color=0xff0000
            )
            await interaction.followup.send(embed=embed)
        finally:
            db.close()

async def setup(bot):
    await bot.add_cog(BrawlCommands(bot))
